import torch  # 导入torch用于张量操作
import torch.nn.init as torch_init  # 导入权重初始化
import torch.nn as nn  # 导入神经网络模块
import math  # 导入数学库
from functools import partial  # 导入partial函数
import torch.nn.functional as F  # 导入常用函数

from layers import *  # 导入自定义层


# DropPath类，用于随机深度正则化


# DropPath类，用于随机深度正则化
class DropPath(nn.Module):
    def __init__(self, drop_prob=0.):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x):
        if self.drop_prob == 0. or not self.training:
            return x
        keep_prob = 1 - self.drop_prob
        shape = (x.shape[0],) + (1,) * (x.ndim - 1)
        random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
        random_tensor.floor_()  # 二值化
        output = x.div(keep_prob) * random_tensor
        return output


# TCA增强器 - 专门增强TCA的核心能力，优化版本
class FeatureAmplifier(nn.Module):
    """
    优化的TCA增强器
    - 减少计算开销，提升效率
    - 增强与TCA和VMRNN的协同效果
    - 采用更轻量级的设计
    """
    def __init__(self, d_model, amplify_ratio=1.0, num_heads=1, dropout=0.05):
        super(FeatureAmplifier, self).__init__()
        self.d_model = d_model

        # 优化：使用更轻量级的特征重标定
        reduction_ratio = 8  # 增加压缩比，减少参数量
        self.feature_recalibration = nn.Sequential(
            nn.AdaptiveAvgPool1d(1),  # 全局平均池化，减少计算
            nn.Conv1d(d_model, d_model // reduction_ratio, 1, bias=False),
            nn.ReLU(inplace=True),  # 使用ReLU替代Tanh，计算更快
            nn.Conv1d(d_model // reduction_ratio, d_model, 1, bias=False),
            nn.Sigmoid()
        )

        # 优化：使用深度可分离卷积减少参数和计算量
        self.temporal_contrast = nn.Sequential(
            nn.Conv1d(d_model, d_model, 3, padding=1, groups=d_model, bias=False),  # 深度卷积
            nn.Conv1d(d_model, d_model, 1, bias=False),  # 点卷积
            nn.BatchNorm1d(d_model),
            nn.ReLU(inplace=True)
        )

        # 优化：自适应融合权重，替代固定的alpha参数
        self.adaptive_fusion = nn.Sequential(
            nn.Linear(d_model * 2, d_model // 4),
            nn.ReLU(inplace=True),
            nn.Linear(d_model // 4, 1),
            nn.Sigmoid()
        )

        # 优化：移除residual_gate，简化结构

    def forward(self, x):
        """
        优化的TCA增强前向传播
        Args:
            x: TCA输出的特征 [batch, seq, d_model]
        Returns:
            enhanced_x: 增强后的特征 [batch, seq, d_model]
        """
        batch_size, seq_len, d_model = x.shape

        # 1. 高效的特征重标定 - 使用全局信息指导局部增强
        x_permuted = x.permute(0, 2, 1)  # [batch, d_model, seq]
        channel_weights = self.feature_recalibration(x_permuted)  # [batch, d_model, 1]
        x_recalibrated = x_permuted * channel_weights  # 广播乘法

        # 2. 时序对比增强 - 使用深度可分离卷积
        x_contrast = self.temporal_contrast(x_permuted)  # [batch, d_model, seq]

        # 3. 自适应融合 - 基于特征内容动态决定融合权重
        # 计算全局特征表示用于融合决策
        global_feat_recal = F.adaptive_avg_pool1d(x_recalibrated, 1).squeeze(-1)  # [batch, d_model]
        global_feat_contrast = F.adaptive_avg_pool1d(x_contrast, 1).squeeze(-1)  # [batch, d_model]
        fusion_input = torch.cat([global_feat_recal, global_feat_contrast], dim=1)  # [batch, 2*d_model]

        alpha = self.adaptive_fusion(fusion_input).unsqueeze(-1)  # [batch, 1, 1]
        x_fused = alpha * x_recalibrated + (1 - alpha) * x_contrast

        # 4. 转回原始维度并应用轻量级残差连接
        x_fused = x_fused.permute(0, 2, 1)  # [batch, seq, d_model]

        # 5. 简化的归一化策略 - 只使用L2归一化，减少计算
        x_out = x + 0.1 * x_fused  # 固定的小权重残差连接
        x_out = F.normalize(x_out, p=2, dim=-1)  # 只使用L2归一化

        return x_out


# 优化的VMRNN相关模块
class VSB(nn.Module):
    def __init__(self, hidden_dim, drop_path=0., norm_layer=nn.LayerNorm, d_state=16):
        super().__init__()
        self.hidden_dim = hidden_dim
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.d_state = d_state

        # 优化：使用单个线性层实现所有门控，减少参数量
        self.gates = nn.Linear(hidden_dim * 2, hidden_dim * 4)  # 一次性计算所有门

        # 优化：简化频率自适应门控，减少层数
        self.freq_gate = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 4),  # 增加压缩比
            nn.ReLU(inplace=True),  # 使用ReLU替代GELU
            nn.Linear(hidden_dim // 4, hidden_dim),
            nn.Sigmoid()
        )

        # 优化：减少注意力头数，提升效率
        self.num_heads = max(1, hidden_dim // 128)  # 动态计算头数
        self.head_dim = hidden_dim // self.num_heads

        # 优化：使用单个投影层替代分离的q,k,v投影
        self.qkv_proj = nn.Linear(hidden_dim, hidden_dim * 3)
        self.out_proj = nn.Linear(hidden_dim, hidden_dim)
        self.attn_scale = self.head_dim ** -0.5
        self.attn_dropout = nn.Dropout(0.1)  # 降低dropout率
        
        # 优化：简化状态空间层，减少参数和计算量
        self.ss_layer = nn.Sequential(
            nn.Linear(hidden_dim, d_state * 2),  # 减少中间维度
            nn.ReLU(inplace=True),  # 使用ReLU替代GELU
            nn.Dropout(0.1),  # 降低dropout率
            nn.Linear(d_state * 2, hidden_dim)
        )

        self.ln_1 = norm_layer(hidden_dim)
        self.ln_2 = norm_layer(hidden_dim)
        
    def _multi_head_attention(self, x):
        B, L, C = x.shape

        # 优化：一次性计算q,k,v，减少线性变换次数
        qkv = self.qkv_proj(x).reshape(B, L, 3, self.num_heads, self.head_dim)
        qkv = qkv.permute(2, 0, 3, 1, 4)  # [3, B, num_heads, L, head_dim]
        q, k, v = qkv[0], qkv[1], qkv[2]

        # 缩放点积注意力
        attn = (q @ k.transpose(-2, -1)) * self.attn_scale  # [B, num_heads, L, L]
        attn = F.softmax(attn, dim=-1)
        attn = self.attn_dropout(attn)

        # 应用注意力并输出
        out = (attn @ v).transpose(1, 2).reshape(B, L, C)  # [B, L, C]
        out = self.out_proj(out)
        return out
        
    def forward(self, x, hx=None, cx=None):
        B, L, C = x.shape

        shortcut = x
        x = self.ln_1(x)

        # 频率自适应门控 - 动态调整特征频率
        freq_gate = self.freq_gate(x)

        # 多头自注意力机制
        attn_out = self._multi_head_attention(x)

        if hx is not None:
            # 确保隐藏状态维度与输入匹配
            if hx.shape[1] != L:
                hx = F.interpolate(hx.permute(0, 2, 1), size=L, mode='linear', align_corners=False).permute(0, 2, 1)
            if cx is not None and cx.shape[1] != L:
                cx = F.interpolate(cx.permute(0, 2, 1), size=L, mode='linear', align_corners=False).permute(0, 2, 1)

            hx = self.ln_1(hx)
            combined = torch.cat((x, hx), dim=-1)

            # 优化：一次性计算所有门控，减少计算次数
            gates = self.gates(combined)  # [B, L, 4*C]
            gates = gates.chunk(4, dim=-1)  # 分割为4个门
            f, i, o, c_tilde = [torch.sigmoid(g) if idx < 3 else torch.tanh(g) for idx, g in enumerate(gates)]

            # 使用频率自适应门控增强状态更新
            f = f * freq_gate
            i = i * freq_gate

            if cx is not None:
                next_c = f * cx + i * c_tilde
            else:
                next_c = i * c_tilde

            # 结合注意力输出和门控输出
            gated_out = o * torch.tanh(next_c)
            x = gated_out + attn_out

        else:
            # 如果没有隐藏状态，仅使用注意力输出
            x = attn_out
            next_c = None

        # 优化：简化残差连接
        x = self.ln_2(x)
        ss_out = self.ss_layer(x)

        # 应用频率自适应门控
        ss_out = ss_out * freq_gate

        # 最终输出
        x = shortcut + self.drop_path(ss_out)

        return x, next_c


class VMRNNCell(nn.Module):
    def __init__(self, hidden_dim, depth=1, drop=0., attn_drop=0., drop_path=0., norm_layer=nn.LayerNorm, d_state=16):
        super().__init__()
        self.depth = depth

        # 优化：限制最大深度，避免过深网络
        self.depth = min(depth, 3)  # 最大深度限制为3

        # 支持不同层次的dropout
        drop_path_rates = [x.item() for x in torch.linspace(0, drop_path, self.depth)] if isinstance(drop_path, float) else drop_path

        # 优化：使用固定状态维度，简化设计
        self.VSBs = nn.ModuleList([
            VSB(hidden_dim=hidden_dim,
                drop_path=drop_path_rates[i],
                norm_layer=norm_layer,
                d_state=d_state)  # 使用固定状态维度
            for i in range(self.depth)
        ])

        # 优化：简化跨层门控机制
        if self.depth > 1:
            self.cross_layer_gates = nn.ModuleList([
                nn.Sequential(
                    nn.Linear(hidden_dim * 2, hidden_dim // 2),
                    nn.ReLU(inplace=True),
                    nn.Linear(hidden_dim // 2, hidden_dim),
                    nn.Sigmoid()
                ) for _ in range(self.depth - 1)
            ])
        
        # 优化：简化层归一化
        if self.depth > 1:
            self.layer_norms = nn.ModuleList([
                norm_layer(hidden_dim)
                for _ in range(self.depth)
            ])

            # 优化：简化层缩放
            self.layer_scale = nn.Parameter(torch.ones(self.depth) * 0.1)

            # 优化：简化特征整合层
            self.output_integration = nn.Sequential(
                nn.Linear(hidden_dim * self.depth, hidden_dim),
                nn.ReLU(inplace=True),
                nn.Dropout(drop)
            )
        else:
            self.layer_norms = None
            self.layer_scale = None
            self.output_integration = nn.Identity()
        
    def forward(self, xt, hidden_states):
        B, L, C = xt.shape

        if hidden_states is None:
            # 初始化状态
            hx = torch.zeros(B, L, C, device=xt.device, dtype=xt.dtype)
            cx = torch.zeros(B, L, C, device=xt.device, dtype=xt.dtype)
            hidden_states = [(hx, cx) for _ in range(self.depth)]
        elif not isinstance(hidden_states[0], tuple):
            # 兼容旧版本的状态格式
            hidden_states = [hidden_states] * self.depth

        layer_outputs = []
        next_hidden_states = []

        # 第一层处理
        hx, cx = hidden_states[0]
        x, next_c = self.VSBs[0](xt, hx, cx)
        layer_outputs.append(x)
        next_hidden_states.append((x, next_c))

        # 处理后续层（如果有多层）
        if self.depth > 1:
            for i in range(1, self.depth):
                hx, cx = hidden_states[i]

                # 优化：简化跨层信息流动
                if i > 1 and self.cross_layer_gates is not None:
                    # 只使用前一层的信息
                    gate_input = torch.cat([layer_outputs[i-2], layer_outputs[-1]], dim=-1)
                    gate = self.cross_layer_gates[i-2](gate_input)
                    layer_input = self.layer_norms[i-1](layer_outputs[-1] + self.layer_scale[i-1] * gate * layer_outputs[i-2])
                else:
                    # 第二层直接使用第一层输出
                    layer_input = layer_outputs[-1]

                # 处理当前层
                x, next_c = self.VSBs[i](layer_input, hx, cx)
                layer_outputs.append(x)
                next_hidden_states.append((x, next_c))

            # 融合多层输出
            all_features = torch.cat(layer_outputs, dim=-1)
            integrated_output = self.output_integration(all_features)
            final_output = integrated_output + layer_outputs[-1]
        else:
            # 单层直接使用输出
            final_output = layer_outputs[-1]

        # 优化：简化最终输出计算
        return final_output, next_hidden_states


# 主编码器模块，包含时序自注意力、卷积降维和归一化
class XEncoder(nn.Module):
    def __init__(self, d_model, hid_dim, out_dim, n_heads, win_size, dropout, gamma, bias, norm=None, use_vmrnn=False, rnn_depth=1, d_state=16, use_amplifier=True, amplify_ratio=1.5):
        super(XEncoder, self).__init__()
        self.n_heads = n_heads  # 注意力头数
        self.win_size = win_size  # 局部窗口大小
        self.use_amplifier = use_amplifier  # 是否使用特征放大器
        self.use_vmrnn = use_vmrnn  # 是否使用VMRNN

        self.self_attn = TCA(d_model, hid_dim, hid_dim, n_heads, norm)  # 时序自注意力层

        # 特征放大器 - 在TCA之后，降维之前
        if self.use_amplifier:
            self.feature_amplifier = FeatureAmplifier(
                d_model=d_model,
                amplify_ratio=amplify_ratio,
                num_heads=min(n_heads * 2, 8),  # 放大器使用更多注意力头
                dropout=dropout
            )

        # 优化的VMRNN集成 - 增强时序建模
        if self.use_vmrnn:
            self.vmrnn = VMRNNCell(
                hidden_dim=d_model,
                depth=rnn_depth,
                drop=dropout,
                attn_drop=dropout,
                drop_path=dropout * 0.3,  # 减少drop_path
                norm_layer=nn.LayerNorm,
                d_state=d_state
            )

            # VMRNN状态管理
            self.vmrnn_states = None

            # 优化：简化TCA-VMRNN融合门控
            self.tca_vmrnn_gate = nn.Sequential(
                nn.Linear(d_model * 2, d_model // 2),
                nn.ReLU(inplace=True),
                nn.Linear(d_model // 2, d_model),
                nn.Sigmoid()
            )

        self.linear1 = nn.Conv1d(d_model, d_model // 2, kernel_size=1)  # 1x1卷积降维
        self.linear2 = nn.Conv1d(d_model // 2, out_dim, kernel_size=1)  # 1x1卷积到输出维度
        self.dropout1 = nn.Dropout(dropout)  # dropout层
        self.dropout2 = nn.Dropout(dropout)
        self.norm1 = nn.LayerNorm(d_model)  # TCA后归一化
        self.norm2 = nn.LayerNorm(d_model)  # VMRNN后归一化
        self.norm3 = nn.LayerNorm(d_model)  # 特征放大器后归一化
        self.loc_adj = DistanceAdj(gamma, bias)  # 距离邻接矩阵

    def forward(self, x, seq_len):
        batch_size, seq_length, d_model = x.shape

        # 1. TCA自注意力处理
        adj = self.loc_adj(batch_size, seq_length)  # [batch, seq, seq] 距离邻接
        mask = self.get_mask(self.win_size, seq_length, seq_len)  # [n_head, batch, seq, seq] 局部mask
        x_tca = x + self.self_attn(x, mask, adj)  # 残差连接自注意力
        x_tca = self.norm1(x_tca)  # TCA后归一化

        # 2. 优化的VMRNN时序记忆建模（如果启用）
        if self.use_vmrnn:
            # 优化：直接使用TCA输出，避免不必要的插值操作
            x_vmrnn, self.vmrnn_states = self.vmrnn(x_tca, self.vmrnn_states)

            # 优化：使用更高效的融合策略
            fusion_input = torch.cat([x_tca, x_vmrnn], dim=-1)  # [batch, seq, 2*d_model]
            fusion_gate = self.tca_vmrnn_gate(fusion_input)  # [batch, seq, d_model]
            x_fused = fusion_gate * x_tca + (1 - fusion_gate) * x_vmrnn
            x_fused = self.norm2(x_fused)  # VMRNN融合后归一化
        else:
            x_fused = x_tca

        # 3. 优化的特征放大器增强（如果启用）
        if self.use_amplifier:
            x_amplified = self.feature_amplifier(x_fused)
            # 优化：使用更小的残差权重，避免过度增强
            x_final = x_fused + 0.1 * x_amplified  # 减小残差权重
            x_final = self.norm3(x_final)  # 特征放大器后归一化
        else:
            x_final = x_fused

        # 4. 优化的特征降维处理
        x_combined = x_final.permute(0, 2, 1)  # 转为[batch, channel, seq]
        x_combined = self.dropout1(F.relu(self.linear1(x_combined)))  # 使用ReLU替代GELU
        x_e = self.dropout2(F.relu(self.linear2(x_combined)))  # 使用ReLU替代GELU

        return x_e, x_combined  # x_e: [batch, out_dim, seq], x_combined: [batch, d_model//2, seq]

    def get_mask(self, window_size, temporal_scale, seq_len):
        # 优化：使用更高效的mask生成方法
        device = next(self.parameters()).device

        # 创建索引矩阵
        indices = torch.arange(temporal_scale, device=device).unsqueeze(0)
        row_indices = indices.expand(temporal_scale, -1)
        col_indices = indices.T.expand(-1, temporal_scale)

        # 计算距离并生成mask
        distances = torch.abs(row_indices - col_indices)
        m = (distances <= window_size // 2).float()

        # 扩展到多头和batch
        m = m.unsqueeze(0).unsqueeze(0).expand(self.n_heads, len(seq_len), -1, -1)

        return m  # [n_head, batch, seq, seq]
        
    def reset_states(self):
        """重置VMRNN的状态"""
        if self.use_vmrnn:
            self.vmrnn_states = None
