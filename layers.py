import torch  # 导入torch用于张量操作
import torch.nn as nn  # 导入神经网络模块
from torch import FloatTensor  # 导入FloatTensor类型
from torch.nn.parameter import Parameter  # 导入Parameter类型
from scipy.spatial.distance import pdist, squareform  # 导入距离计算函数
import torch.nn.functional as F  # 导入常用函数
import numpy as np  # 导入numpy
import math  # 导入数学库


class DistanceAdj(nn.Module):
    def __init__(self, sigma, bias):
        super(DistanceAdj, self).__init__()
        self.w = nn.Parameter(torch.FloatTensor(1))  # 可学习参数w
        self.b = nn.Parameter(torch.FloatTensor(1))  # 可学习参数b
        self.w.data.fill_(sigma)  # 初始化w
        self.b.data.fill_(bias)  # 初始化b

        # 缓存机制 - 避免重复计算相同长度的距离矩阵
        self._cache = {}
        self._max_cache_size = 10  # 最多缓存10个不同长度的矩阵

    def forward(self, batch_size, seq_len):
        # 优化：检查缓存，包含设备信息
        cache_key = (seq_len, self.w.device)
        if cache_key in self._cache:
            base_dist = self._cache[cache_key]
        else:
            # 优化：使用更高效的距离矩阵计算
            indices = torch.arange(seq_len, dtype=torch.float32, device=self.w.device)
            dist_matrix = torch.abs(indices.unsqueeze(0) - indices.unsqueeze(1))  # 广播计算

            # 缓存基础距离矩阵，包含设备信息
            if len(self._cache) < self._max_cache_size:
                self._cache[cache_key] = dist_matrix
            base_dist = dist_matrix

        # 优化：简化距离变换计算
        dist = torch.exp(-self.w * base_dist ** 2 + self.b)  # 简化公式

        # 优化：使用view替代expand，减少内存使用
        dist = dist.unsqueeze(0)
        if batch_size > 1:
            dist = dist.expand(batch_size, -1, -1)

        return dist  # [batch, seq_len, seq_len]


class TCA(nn.Module):
    def __init__(self, d_model, dim_k, dim_v, n_heads, norm=None):
        super(TCA, self).__init__()
        self.dim_v = dim_v  # value通道数
        self.dim_k = dim_k  # key/query通道数
        self.n_heads = n_heads  # 注意力头数
        self.norm = norm  # 是否归一化

        self.q = nn.Linear(d_model, dim_k)  # query投影
        self.k = nn.Linear(d_model, dim_k)  # key投影
        self.v = nn.Linear(d_model, dim_v)  # value投影
        self.o = nn.Linear(dim_v, d_model)  # 输出投影

        self.norm_fact = 1 / math.sqrt(dim_k)  # 缩放因子
        self.alpha = nn.Parameter(torch.tensor(0.))  # 可学习融合系数
        self.act = nn.Softmax(dim=-1)  # softmax激活

    def forward(self, x, mask, adj=None):
        batch_size, seq_len, d_model = x.shape
        head_dim = self.dim_k // self.n_heads

        # 优化：重新组织Q,K,V的计算和reshape
        Q = self.q(x).view(batch_size, seq_len, self.n_heads, head_dim).transpose(1, 2)  # [batch, n_head, seq, head_dim]
        K = self.k(x).view(batch_size, seq_len, self.n_heads, head_dim).transpose(1, 2)
        V = self.v(x).view(batch_size, seq_len, self.n_heads, head_dim).transpose(1, 2)

        # 优化：计算注意力分数
        attn_scores = torch.matmul(Q, K.transpose(-2, -1)) * self.norm_fact  # [batch, n_head, seq, seq]

        if adj is not None:
            # 优化：邻接矩阵的广播加法
            attn_scores = attn_scores + adj.unsqueeze(1)  # 广播到多头维度

        # 全局和局部注意力
        g_map = attn_scores
        l_map = attn_scores.masked_fill(mask.eq(0), -1e9)  # 局部mask

        g_map = self.act(g_map)  # 全局softmax
        l_map = self.act(l_map)  # 局部softmax

        # 优化：计算加权和
        glb = torch.matmul(g_map, V).transpose(1, 2).contiguous().view(batch_size, seq_len, -1)  # 全局加权和
        lcl = torch.matmul(l_map, V).transpose(1, 2).contiguous().view(batch_size, seq_len, -1)  # 局部加权和

        alpha = torch.sigmoid(self.alpha)  # 融合系数
        tmp = alpha * glb + (1 - alpha) * lcl  # 全局与局部融合

        # 优化：简化归一化
        if self.norm:
            tmp = F.normalize(tmp, p=2, dim=-1)  # 只使用L2归一化

        tmp = self.o(tmp)  # 输出投影
        return tmp  # [batch, seq, d_model]
